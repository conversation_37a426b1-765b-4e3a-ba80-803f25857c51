.nav {
   display: flex;
   gap: 1rem;
   align-items: center;
   padding: 2.5rem 6rem;
   position: sticky;
   top: 0;
   transition: all 0.2s ease-in-out;
   z-index: 1000;
   position: relative;

   @media (max-width: 1024px) {
      padding: 2.5rem 3rem;
   }

   @media (max-width: 425px) {
      padding: 2.5rem 2rem;
   }

   &.scrolled {
      backdrop-filter: blur(10px);
      background: rgba(0, 0, 0, 0.5);
      padding-top: 1.5rem;
      padding-bottom: 1.5rem;
   }

   &_btn_secondary {
      padding: 0.6rem 2.5rem;
      font-size: 1.5rem;
      border-radius: 1rem;
      font-weight: 500;
      background: transparent;
      border: 2px solid #868686;
   }

   &_logo {
      margin-bottom: -1rem;
      transition: all 0.2s ease-in-out;

      img {
         transition: all 0.2s ease-in-out;
      }
   }

   &.scrolled &_logo {
      margin-bottom: -0.5rem;

      img {
         width: 100px !important;
         height: auto !important;
      }
   }

   &_links {
      display: flex;
      gap: 5rem;
      margin: 0 auto;
      list-style-type: none;
      position: absolute;
      transform: translateX(-50%);
      left: 50%;

      @media (max-width: 850px) {
         display: none;
      }
   }

   &_link {
      font-size: 1.4rem;
      position: relative;

      a {
         padding: 1rem 0;
      }

      .active {
         position: absolute;
         bottom: -0.2rem;
         left: 0;
         height: 2px;
         width: 100%;
         margin: 0 auto;
         background: linear-gradient(to right, #ff0000 50%, #f5eb0c);
         border-radius: 2rem;
      }
   }

   &_btn {
      padding: 0.8rem 3rem;
      font-size: 1.5rem;
      border-radius: 0.7rem;
      transition: all 0.2s ease-in-out;

      @media (max-width: 1024px) {
         margin-left: auto;
      }
   }

   &.scrolled &_btn {
      padding: 0.7rem 2.7rem;
      font-size: 1.4rem;
   }

   .desktop_auth {
      transition: all 0.2s ease-in-out;
      margin-left: auto;

      @media (max-width: 850px) {
         display: none;
      }
   }
}
