.home {
   // display: grid;
   // grid-template-columns: 1fr 35rem;
   // grid-template-columns: 1fr 32.5rem;
   // // grid-template-rows: 50rem 1fr;
   // gap: 4rem;
   // row-gap: 3rem;
   // padding: 0rem 6rem;
   // min-height: calc(100svh - 8.5rem);
   // max-width: 1440px;
   // max-width: 1140px;
   // max-width: 1240px;
   margin: 0 auto;
   display: flex;
   flex-direction: column;
   gap: 3rem;

   .grid {
      display: grid;
      grid-template-columns: 1fr 35rem;
      grid-template-columns: 1fr 32.5rem;
      gap: 4rem;
      row-gap: 3rem;

      @media (max-width: 1024px) {
         grid-template-columns: 1fr;
      }
   }

   .main {
      grid-row: 1 / -1;
      display: flex;
      flex-direction: column;
      gap: 2rem;
   }

   .sidebar {
      grid-column: 2 / -1;
      display: flex;
      flex-direction: column;
      gap: 2rem;
      justify-content: flex-start;

      @media (max-width: 1024px) {
         grid-column: 1 / -1;
      }
   }
}

.ads_container {
   display: flex;
   justify-content: center;
   min-height: 50rem;
}

.ads {
   margin: 0 auto;
   position: sticky;
   top: 12rem;
   border-radius: 1rem;
   margin-bottom: 4rem;
}
